name: bidtrakr
description: "Drivers Performance, Income Tracking and Spare Parts Monitoring App"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  # State management
  flutter_hooks: ^0.21.2
  flutter_riverpod: ^2.6.1
  hooks_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  riverpod: ^2.6.1

  # Database
  drift: ^2.28.0
  sqlite3_flutter_libs: ^0.5.37
  sqflite: ^2.4.1
  path: ^1.9.1
  path_provider: ^2.1.5

  # Code generation
  freezed_annotation: ^3.1.0
  json_annotation: ^4.9.0

  # UI
  google_fonts: ^6.2.1
  flutter_svg: ^2.2.0
  intl: ^0.20.2
  dartz: ^0.10.1
  fl_chart: ^1.0.0

  # File operations
  shared_preferences: ^2.5.3
  device_info_plus: ^11.5.0
  file_picker: ^10.2.0
  permission_handler: ^12.0.1
  shimmer: ^3.0.0

  # Sync and Cloud
  supabase_flutter: ^2.9.1
  uuid: ^4.5.1
  connectivity_plus: ^6.1.4
  flutter_dotenv: ^5.2.1
  url_launcher: ^6.3.2
  supabase: ^2.8.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0

  # Code generation
  build_runner: ^2.5.4
  drift_dev: ^2.28.0
  freezed: ^3.1.0
  json_serializable: ^6.9.5
  riverpod_generator: ^2.6.5

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - .env
